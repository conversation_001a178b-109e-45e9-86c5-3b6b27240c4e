using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;
using ZeroDateStrat.Services;
using ZeroDateStrat.Utils;

namespace ZeroDateStrat.Strategies;

public interface IZeroDteStrategy
{
    Task<List<TradingSignal>> GenerateSignalsAsync();
    Task<bool> ExecuteSignalAsync(TradingSignal signal);
    Task ManagePositionsAsync();
    Task<bool> ShouldTrade();
}

public class ZeroDteStrategy : IZeroDteStrategy
{
    private readonly ILogger<ZeroDteStrategy> _logger;
    private readonly IConfiguration _configuration;
    private readonly IAlpacaService _alpacaService;
    private readonly IOptionsScanner _optionsScanner;
    private readonly IRiskManager _riskManager;
    private readonly List<Position> _activePositions = new();

    // Focus on SPX for best 0 DTE trading (tax advantages, cash settlement, no assignment risk)
    private readonly List<string> _watchlist = new()
    {
        "SPX", "SPY" // SPX primary, SPY backup
    };

    public ZeroDteStrategy(
        ILogger<ZeroDteStrategy> logger,
        IConfiguration configuration,
        IAlpacaService alpacaService,
        IOptionsScanner optionsScanner,
        IRiskManager riskManager)
    {
        _logger = logger;
        _configuration = configuration;
        _alpacaService = alpacaService;
        _optionsScanner = optionsScanner;
        _riskManager = riskManager;
    }

    public async Task<bool> ShouldTrade()
    {
        try
        {
            var now = DateTime.Now;
            var entryStart = TimeSpan.Parse(_configuration["Trading:EntryTimeStart"] ?? "09:45:00");
            var entryEnd = TimeSpan.Parse(_configuration["Trading:EntryTimeEnd"] ?? "10:30:00");
            var managementTime = TimeSpan.Parse(_configuration["Trading:ManagementTime"] ?? "14:00:00");
            var forceClose = TimeSpan.Parse(_configuration["Trading:ForceCloseTime"] ?? "15:45:00");
            var tradingEnd = TimeSpan.Parse(_configuration["Trading:TradingEndTime"] ?? "16:00:00");

            // Check if within any trading window
            var inEntryWindow = now.TimeOfDay >= entryStart && now.TimeOfDay <= entryEnd;
            var inManagementWindow = now.TimeOfDay > entryEnd && now.TimeOfDay < forceClose;
            var inTradingHours = now.TimeOfDay < tradingEnd;

            if (!inTradingHours)
            {
                _logger.LogInformation("Outside trading hours");
                return false;
            }

            // Only allow new entries during entry window
            if (!inEntryWindow && !inManagementWindow)
            {
                _logger.LogInformation("Outside entry window - only position management allowed");
                return false;
            }

            // Check if it's a weekday
            if (now.DayOfWeek == DayOfWeek.Saturday || now.DayOfWeek == DayOfWeek.Sunday)
            {
                _logger.LogInformation("Weekend - no trading");
                return false;
            }

            // Check account status and buying power
            var account = await _alpacaService.GetAccountAsync();
            if (account == null)
            {
                _logger.LogWarning("Could not retrieve account information");
                return false;
            }

            // Basic account validation
            if (account.Equity < 1000)
            {
                _logger.LogWarning("Insufficient account equity");
                return false;
            }

            // Check daily loss limit
            var maxDailyLoss = _configuration.GetValue<decimal>("Trading:MaxDailyLoss", 500);
            if (await _riskManager.GetDailyPnLAsync() <= -maxDailyLoss)
            {
                _logger.LogWarning($"Daily loss limit reached: {maxDailyLoss:C}");
                return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if should trade");
            return false;
        }
    }

    public async Task<List<TradingSignal>> GenerateSignalsAsync()
    {
        try
        {
            if (!await ShouldTrade())
                return new List<TradingSignal>();

            _logger.LogInformation("Scanning for 0 DTE trading opportunities...");

            // Scan for 0 DTE options
            var optionChains = await _optionsScanner.ScanForZeroDteOptionsAsync(_watchlist);
            
            if (!optionChains.Any())
            {
                _logger.LogInformation("No 0 DTE options found");
                return new List<TradingSignal>();
            }

            // Find trading opportunities
            var signals = await _optionsScanner.FindTradingOpportunitiesAsync(optionChains);

            // Apply risk management filters
            var filteredSignals = new List<TradingSignal>();
            foreach (var signal in signals)
            {
                if (await _riskManager.ValidateSignalAsync(signal))
                {
                    filteredSignals.Add(signal);
                }
            }

            _logger.LogInformation($"Generated {filteredSignals.Count} valid trading signals");
            return filteredSignals;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating trading signals");
            return new List<TradingSignal>();
        }
    }

    public async Task<bool> ExecuteSignalAsync(TradingSignal signal)
    {
        try
        {
            _logger.LogInformation($"Executing signal: {signal.Strategy} for {signal.UnderlyingSymbol}");

            // Final risk check before execution
            if (!await _riskManager.ValidateSignalAsync(signal))
            {
                _logger.LogWarning($"Signal failed final risk validation: {signal.Id}");
                return false;
            }

            // Calculate position size
            var positionSize = await _riskManager.CalculatePositionSizeAsync(signal);
            if (positionSize <= 0)
            {
                _logger.LogWarning($"Invalid position size calculated: {positionSize}");
                return false;
            }

            // Adjust quantities based on position size
            foreach (var leg in signal.Legs)
            {
                leg.Quantity = (int)(leg.Quantity * positionSize);
            }

            // Place the order
            var order = await _alpacaService.PlaceOrderAsync(signal);
            if (order == null)
            {
                _logger.LogError($"Failed to place order for signal: {signal.Id}");
                return false;
            }

            // Create position tracking
            var position = new Position
            {
                Strategy = signal.Strategy,
                UnderlyingSymbol = signal.UnderlyingSymbol,
                Legs = signal.Legs,
                OpenCredit = signal.Legs.Where(l => l.Side == OrderSide.Sell).Sum(l => l.Price * l.Quantity) -
                           signal.Legs.Where(l => l.Side == OrderSide.Buy).Sum(l => l.Price * l.Quantity),
                OpenedAt = DateTime.UtcNow,
                ExpirationDate = signal.ExpirationDate,
                ProfitTarget = _configuration.GetValue<decimal>($"Strategies:{signal.Strategy}:ProfitTarget", 0.5m),
                StopLoss = _configuration.GetValue<decimal>($"Strategies:{signal.Strategy}:StopLoss", 0.8m)
            };

            _activePositions.Add(position);

            signal.IsExecuted = true;
            signal.ExecutedAt = DateTime.UtcNow;

            _logger.LogInformation($"Successfully executed signal: {signal.Id}, Order ID: {order.OrderId}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error executing signal: {signal.Id}");
            return false;
        }
    }

    public async Task ManagePositionsAsync()
    {
        try
        {
            _logger.LogInformation($"Managing {_activePositions.Count} active positions");

            var positionsToClose = new List<Position>();

            foreach (var position in _activePositions.Where(p => p.Status == PositionStatus.Open))
            {
                // Update position value and P&L
                await UpdatePositionValue(position);

                // Check if position should be closed
                if (position.ShouldClose)
                {
                    positionsToClose.Add(position);
                }
            }

            // Close positions that meet exit criteria
            foreach (var position in positionsToClose)
            {
                await ClosePositionAsync(position);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error managing positions");
        }
    }

    private async Task UpdatePositionValue(Position position)
    {
        try
        {
            decimal currentValue = 0;

            foreach (var leg in position.Legs)
            {
                // In a real implementation, you'd get current option prices
                // For now, we'll use a simplified approach
                var currentPrice = leg.Price; // Placeholder
                
                if (leg.Side == OrderSide.Sell)
                    currentValue -= currentPrice * leg.Quantity;
                else
                    currentValue += currentPrice * leg.Quantity;
            }

            position.CurrentValue = currentValue;
            position.UnrealizedPnL = position.OpenCredit + currentValue;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error updating position value for {position.Id}");
        }
    }

    private async Task<bool> ClosePositionAsync(Position position)
    {
        try
        {
            _logger.LogInformation($"Closing position: {position.Id} ({position.Strategy})");

            // In a real implementation, you'd place closing orders for each leg
            // For now, we'll mark it as closed
            position.Status = PositionStatus.Closed;
            position.ClosedAt = DateTime.UtcNow;

            var realizedPnL = position.UnrealizedPnL;
            _logger.LogInformation($"Position closed with P&L: {realizedPnL:C2}");

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error closing position: {position.Id}");
            return false;
        }
    }
}
