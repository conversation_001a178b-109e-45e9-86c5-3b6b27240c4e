using Alpaca.Markets;

namespace ZeroDateStrat.Models;

public class OptionContract
{
    public string Symbol { get; set; } = string.Empty;
    public string UnderlyingSymbol { get; set; } = string.Empty;
    public DateTime ExpirationDate { get; set; }
    public decimal StrikePrice { get; set; }
    public OptionType OptionType { get; set; }
    public decimal Bid { get; set; }
    public decimal Ask { get; set; }
    public decimal LastPrice { get; set; }
    public decimal Volume { get; set; }
    public decimal OpenInterest { get; set; }
    public decimal ImpliedVolatility { get; set; }
    public decimal Delta { get; set; }
    public decimal Gamma { get; set; }
    public decimal Theta { get; set; }
    public decimal Vega { get; set; }
    public DateTime LastUpdated { get; set; }

    public decimal MidPrice => (Bid + Ask) / 2;
    public decimal Spread => Ask - Bid;
    public decimal SpreadPercentage => Bid > 0 ? (Spread / Bid) * 100 : 0;
    public int DaysToExpiration => (ExpirationDate.Date - DateTime.Today).Days;
    public bool IsZeroDte => DaysToExpiration == 0;
    public bool IsLiquid => Volume > 10 && OpenInterest > 50 && SpreadPercentage < 10;
}

public enum OptionType
{
    Call,
    Put
}

public class OptionChain
{
    public string UnderlyingSymbol { get; set; } = string.Empty;
    public decimal UnderlyingPrice { get; set; }
    public DateTime ExpirationDate { get; set; }
    public List<OptionContract> Calls { get; set; } = new();
    public List<OptionContract> Puts { get; set; } = new();
    public DateTime LastUpdated { get; set; }

    public OptionContract? GetCallByStrike(decimal strike)
    {
        return Calls.FirstOrDefault(c => Math.Abs(c.StrikePrice - strike) < 0.01m);
    }

    public OptionContract? GetPutByStrike(decimal strike)
    {
        return Puts.FirstOrDefault(p => Math.Abs(p.StrikePrice - strike) < 0.01m);
    }

    public OptionContract? GetAtmCall()
    {
        return Calls.OrderBy(c => Math.Abs(c.StrikePrice - UnderlyingPrice)).FirstOrDefault();
    }

    public OptionContract? GetAtmPut()
    {
        return Puts.OrderBy(p => Math.Abs(p.StrikePrice - UnderlyingPrice)).FirstOrDefault();
    }
}
