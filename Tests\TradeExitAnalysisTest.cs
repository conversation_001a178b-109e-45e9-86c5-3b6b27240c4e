using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;
using ZeroDateStrat.Services;
using ZeroDateStrat.Utils;

namespace ZeroDateStrat.Tests;

public class TradeExitAnalysisTest
{
    private readonly ILogger<TradeExitAnalysisTest> _logger;
    private readonly IServiceProvider _serviceProvider;

    public TradeExitAnalysisTest()
    {
        // Setup dependency injection for testing
        var services = new ServiceCollection();
        
        // Add logging with debug level
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.SetMinimumLevel(LogLevel.Debug);
        });
        
        // Add configuration
        var configuration = new ConfigurationBuilder()
            .AddJsonFile("appsettings.json", optional: false)
            .Build();
        services.AddSingleton<IConfiguration>(configuration);
        
        // Add services
        services.AddScoped<IHistoricalDataService, HistoricalDataService>();
        services.AddScoped<IBacktestingEngine, BacktestingEngine>();
        services.AddScoped<IPerformanceAnalytics, PerformanceAnalytics>();
        services.AddScoped<IAlpacaService, AlpacaService>();
        services.AddScoped<IOptionsScanner, OptionsScanner>();
        services.AddScoped<IRiskManager, RiskManager>();
        services.AddScoped<IMarketRegimeAnalyzer, MarketRegimeAnalyzer>();
        
        _serviceProvider = services.BuildServiceProvider();
        _logger = _serviceProvider.GetRequiredService<ILogger<TradeExitAnalysisTest>>();
    }

    public async Task AnalyzeTradeExits()
    {
        Console.WriteLine("=== Trade Exit Analysis Test ===\n");

        try
        {
            var backtestingEngine = _serviceProvider.GetRequiredService<IBacktestingEngine>();

            // Run a short backtest to analyze trade exits
            var config = new BacktestConfiguration
            {
                StrategyName = "Exit_Analysis",
                StartDate = DateTime.Today.AddDays(-7), // Just one week
                EndDate = DateTime.Today.AddDays(-1),
                InitialCapital = 10000m,
                Symbols = new List<string> { "SPY" },
                Parameters = new Dictionary<string, object>
                {
                    ["WingWidth"] = 10m,
                    ["MinCredit"] = 0.05m,
                    ["MaxCredit"] = 2.00m
                }
            };

            Console.WriteLine($"Running exit analysis from {config.StartDate:yyyy-MM-dd} to {config.EndDate:yyyy-MM-dd}");

            var result = await backtestingEngine.RunBacktestAsync(config);

            Console.WriteLine($"\n=== Exit Analysis Results ===");
            Console.WriteLine($"Total Trades: {result.TotalTrades}");
            Console.WriteLine($"Initial Capital: {result.InitialCapital:C2}");
            Console.WriteLine($"Final Capital: {result.FinalCapital:C2}");
            Console.WriteLine($"Total Return: {result.TotalReturn:P2}");

            // Analyze each trade in detail
            Console.WriteLine($"\n=== Individual Trade Analysis ===");
            foreach (var trade in result.Trades.Take(10)) // Show first 10 trades
            {
                Console.WriteLine($"\nTrade {trade.Id}: {trade.Strategy}");
                Console.WriteLine($"  Entry Time: {trade.EntryTime:yyyy-MM-dd HH:mm}");
                Console.WriteLine($"  Entry Price: {trade.EntryPrice:C2}");
                Console.WriteLine($"  Exit Time: {(trade.ExitTime?.ToString("yyyy-MM-dd HH:mm") ?? "Still Open")}");
                Console.WriteLine($"  Exit Price: {(trade.ExitPrice?.ToString("C2") ?? "N/A")}");
                Console.WriteLine($"  Realized P&L: {trade.RealizedPnL:C2}");
                Console.WriteLine($"  Exit Reason: {trade.ExitReason ?? "N/A"}");
                Console.WriteLine($"  Holding Period: {trade.HoldingPeriod}");
                Console.WriteLine($"  Commission: {trade.Commission:C2}");

                // Show leg details
                Console.WriteLine($"  Legs ({trade.Legs.Count}):");
                foreach (var leg in trade.Legs)
                {
                    Console.WriteLine($"    {leg.Side} {leg.Quantity} {leg.OptionType} {leg.StrikePrice} @ {leg.Price:C2}");
                }

                // Calculate expected vs actual
                var expectedCredit = trade.Legs.Sum(l => l.Price * l.Quantity * (l.Side == OrderSide.Sell ? 1 : -1));
                Console.WriteLine($"  Expected Credit: {expectedCredit:C2}");
                Console.WriteLine($"  Entry Price vs Expected: {(trade.EntryPrice - expectedCredit):C2} difference");
            }

            // Analyze exit reasons
            Console.WriteLine($"\n=== Exit Reason Analysis ===");
            var exitReasons = result.Trades
                .Where(t => !string.IsNullOrEmpty(t.ExitReason))
                .GroupBy(t => t.ExitReason)
                .Select(g => new { Reason = g.Key, Count = g.Count(), AvgPnL = g.Average(t => t.RealizedPnL) })
                .OrderByDescending(x => x.Count);

            foreach (var reason in exitReasons)
            {
                Console.WriteLine($"  {reason.Reason}: {reason.Count} trades, Avg P&L: {reason.AvgPnL:C2}");
            }

            // Check for open positions
            var openTrades = result.Trades.Where(t => !t.ExitTime.HasValue).ToList();
            Console.WriteLine($"\n=== Open Positions ===");
            Console.WriteLine($"Open trades: {openTrades.Count}");

            foreach (var openTrade in openTrades.Take(5))
            {
                Console.WriteLine($"  Trade {openTrade.Id}: {openTrade.Strategy}, Entry: {openTrade.EntryTime:yyyy-MM-dd}, Entry Price: {openTrade.EntryPrice:C2}");
            }

            // Daily performance analysis
            Console.WriteLine($"\n=== Daily Performance ===");
            foreach (var daily in result.DailyPerformance.TakeLast(5))
            {
                Console.WriteLine($"{daily.Date:yyyy-MM-dd}: Portfolio {daily.PortfolioValue:C2}, " +
                                $"Daily P&L: {daily.DailyPnL:C2}, " +
                                $"Return: {daily.DailyReturn:P2}, " +
                                $"Active: {daily.ActivePositions}");
            }

        }
        catch (Exception ex)
        {
            Console.WriteLine($"Exit analysis failed: {ex.Message}");
            _logger.LogError(ex, "Exit analysis failed");
        }
    }

    public static async Task RunExitAnalysisTest()
    {
        var test = new TradeExitAnalysisTest();
        await test.AnalyzeTradeExits();
    }
}
