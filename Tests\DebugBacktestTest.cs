using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Reflection;
using ZeroDateStrat.Models;
using ZeroDateStrat.Services;
using ZeroDateStrat.Utils;

namespace ZeroDateStrat.Tests;

public class DebugBacktestTest
{
    private readonly ILogger<DebugBacktestTest> _logger;
    private readonly IServiceProvider _serviceProvider;

    public DebugBacktestTest()
    {
        // Setup dependency injection for testing
        var services = new ServiceCollection();
        
        // Add logging with debug level
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.SetMinimumLevel(LogLevel.Debug);
            builder.AddFilter("ZeroDateStrat.Services.BacktestingEngine", LogLevel.Debug);
        });
        
        // Add configuration
        var configuration = new ConfigurationBuilder()
            .AddJsonFile("appsettings.json", optional: false)
            .Build();
        services.AddSingleton<IConfiguration>(configuration);
        
        // Add services
        services.AddScoped<IHistoricalDataService, HistoricalDataService>();
        services.AddScoped<IBacktestingEngine, BacktestingEngine>();
        services.AddScoped<IPerformanceAnalytics, PerformanceAnalytics>();
        services.AddScoped<IAlpacaService, AlpacaService>();
        services.AddScoped<IOptionsScanner, OptionsScanner>();
        services.AddScoped<IRiskManager, RiskManager>();
        services.AddScoped<IMarketRegimeAnalyzer, MarketRegimeAnalyzer>();
        
        _serviceProvider = services.BuildServiceProvider();
        _logger = _serviceProvider.GetRequiredService<ILogger<DebugBacktestTest>>();
    }

    public async Task TestSignalGeneration()
    {
        Console.WriteLine("=== Debug Signal Generation Test ===\n");

        try
        {
            var historicalDataService = _serviceProvider.GetRequiredService<IHistoricalDataService>();
            var backtestingEngine = _serviceProvider.GetRequiredService<IBacktestingEngine>();

            // Test a single day
            var testDate = DateTime.Today.AddDays(-5);
            Console.WriteLine($"Testing signal generation for {testDate:yyyy-MM-dd}");

            // Get historical data
            var optionContracts = await historicalDataService.GetHistoricalOptionChainAsync("SPY", testDate);
            var underlyingPrice = await historicalDataService.GetHistoricalPriceAsync("SPY", testDate);

            Console.WriteLine($"Underlying price: {underlyingPrice:C2}");
            Console.WriteLine($"Total option contracts: {optionContracts.Count}");

            var calls = optionContracts.Where(o => o.OptionType == OptionType.Call).ToList();
            var puts = optionContracts.Where(o => o.OptionType == OptionType.Put).ToList();

            Console.WriteLine($"Calls: {calls.Count}, Puts: {puts.Count}");

            // Check delta distribution
            var putDeltas = puts.Select(p => p.Delta).ToList();
            var callDeltas = calls.Select(c => c.Delta).ToList();

            Console.WriteLine($"Put deltas range: {putDeltas.Min():F3} to {putDeltas.Max():F3}");
            Console.WriteLine($"Call deltas range: {callDeltas.Min():F3} to {callDeltas.Max():F3}");

            // Show some specific deltas
            Console.WriteLine("\nSample put deltas:");
            foreach (var put in puts.Take(5))
            {
                Console.WriteLine($"  Strike {put.StrikePrice:F2}: Delta {put.Delta:F3}");
            }

            Console.WriteLine("\nSample call deltas:");
            foreach (var call in calls.Take(5))
            {
                Console.WriteLine($"  Strike {call.StrikePrice:F2}: Delta {call.Delta:F3}");
            }

            // Check suitable puts for credit spreads (updated criteria)
            var suitablePuts = puts.Where(p =>
                Math.Abs(p.Delta) >= 0.10m &&
                Math.Abs(p.Delta) <= 0.40m &&
                p.Volume > 5 &&
                p.StrikePrice < underlyingPrice).ToList();

            Console.WriteLine($"Suitable puts for credit spreads: {suitablePuts.Count}");

            // Debug each put to see why they're filtered out
            Console.WriteLine("\nDebugging put filtering:");
            foreach (var put in puts.Take(10))
            {
                var deltaAbs = Math.Abs(put.Delta);
                var deltaOk = deltaAbs >= 0.10m && deltaAbs <= 0.40m;
                var volumeOk = put.Volume > 5;
                var strikeOk = put.StrikePrice < underlyingPrice;

                Console.WriteLine($"  Strike {put.StrikePrice:F2}: Delta {put.Delta:F3} (abs={deltaAbs:F3}, ok={deltaOk}), Volume {put.Volume} (ok={volumeOk}), Strike<Underlying {strikeOk}");
            }

            foreach (var put in suitablePuts.Take(5))
            {
                Console.WriteLine($"  Put: Strike {put.StrikePrice:F2}, Delta {put.Delta:F3}, Volume {put.Volume}, Price {put.MidPrice:F2}");
                
                // Check for long put
                var longPutStrike = put.StrikePrice - 10m;
                var longPut = puts.FirstOrDefault(p => Math.Abs(p.StrikePrice - longPutStrike) < 0.01m);
                
                if (longPut != null)
                {
                    var credit = put.MidPrice - longPut.MidPrice;
                    Console.WriteLine($"    Long put: Strike {longPut.StrikePrice:F2}, Price {longPut.MidPrice:F2}, Credit: {credit:F2}");
                }
                else
                {
                    Console.WriteLine($"    No long put found at strike {longPutStrike:F2}");
                }
            }

            // Test actual backtest
            var config = new BacktestConfiguration
            {
                StrategyName = "Debug_Test",
                StartDate = testDate,
                EndDate = testDate,
                InitialCapital = 10000m,
                Symbols = new List<string> { "SPY" }
            };

            Console.WriteLine("\n=== Testing Signal Generation in Backtest ===");

            // Test the signal generation directly using reflection to access private method
            var backtestType = backtestingEngine.GetType();
            var generateSignalsMethod = backtestType.GetMethod("GenerateSignalsForDate",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            if (generateSignalsMethod != null)
            {
                var signals = await (Task<List<TradingSignal>>)generateSignalsMethod.Invoke(
                    backtestingEngine,
                    new object[] { new List<string> { "SPY" }, testDate, config });

                Console.WriteLine($"Generated {signals.Count} signals directly");

                foreach (var signal in signals)
                {
                    Console.WriteLine($"Signal: {signal.Strategy}, IsValid: {signal.IsValid}, Confidence: {signal.Confidence:F3}, RiskReward: {signal.RiskRewardRatio:F3}");
                    Console.WriteLine($"  ExpectedProfit: {signal.ExpectedProfit:C2}, MaxLoss: {signal.MaxLoss:C2}");
                    Console.WriteLine($"  Legs: {signal.Legs.Count}");

                    // Test validation manually
                    var portfolio = new BacktestPortfolio(10000m);
                    var requiredCapital = Math.Abs(signal.MaxLoss);
                    var maxRisk = portfolio.Cash * 0.1m;

                    Console.WriteLine($"  Validation: Required capital {requiredCapital:C2}, Max risk {maxRisk:C2}, Passes: {requiredCapital <= maxRisk}");
                }
            }

            var result = await backtestingEngine.RunBacktestAsync(config);
            Console.WriteLine($"\nBacktest result: {result.TotalTrades} trades generated");

        }
        catch (Exception ex)
        {
            Console.WriteLine($"Debug test failed: {ex.Message}");
            _logger.LogError(ex, "Debug test failed");
        }
    }

    public static async Task RunDebugTest()
    {
        var test = new DebugBacktestTest();
        await test.TestSignalGeneration();
    }
}
